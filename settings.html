<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Bullets - Paramètres</title>
  <style>
    body {
      font-family: 'Open Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      width: 500px;
      background-color: #f9f9f9;
      color: #333;
    }

    h1 {
      font-size: 24px;
      margin-top: 0;
      margin-bottom: 20px;
      color: #339133;
      display: flex;
      align-items: center;
    }

    h1 img {
      height: 25px;
      width: 25px;
      margin-right: 10px;
    }

    h2 {
      font-size: 18px;
      margin-bottom: 15px;
      margin-top: 25px;
      color: #339133;
      border-bottom: 2px solid #339133;
      padding-bottom: 5px;
    }

    .setting-group {
      background: white;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .setting-item {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 600;
      color: #333;
    }

    select, input[type="text"], textarea {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }

    select:focus, input[type="text"]:focus, textarea:focus {
      outline: none;
      border-color: #339133;
    }

    textarea {
      min-height: 100px;
      resize: vertical;
      font-family: inherit;
    }

    .button {
      background: #339133;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
      margin-right: 10px;
    }

    .button:hover {
      background: #2b7a2b;
    }

    .button.secondary {
      background: #666;
    }

    .button.secondary:hover {
      background: #555;
    }

    .button.danger {
      background: #dc3545;
    }

    .button.danger:hover {
      background: #c82333;
    }

    .custom-language-input {
      margin-top: 10px;
      display: none;
    }

    .prompt-template {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 15px;
    }

    .prompt-template h4 {
      margin: 0 0 10px 0;
      color: #339133;
    }

    .prompt-actions {
      margin-top: 10px;
    }

    .success-message {
      background: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      display: none;
    }

    .back-link {
      color: #339133;
      text-decoration: none;
      margin-bottom: 20px;
      display: inline-block;
    }

    .back-link:hover {
      text-decoration: underline;
    }

    /* Dark mode styles */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1e1e1e;
        color: #ccc;
      }

      h1, h2 {
        color: #4caf50;
      }

      h2 {
        border-bottom-color: #4caf50;
      }

      .setting-group {
        background: #2d2d2d;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }

      label {
        color: #ccc;
      }

      select, input[type="text"], textarea {
        background: #1a1a1a;
        border-color: #444;
        color: #ccc;
      }

      select:focus, input[type="text"]:focus, textarea:focus {
        border-color: #4caf50;
      }

      .prompt-template {
        background: #1a1a1a;
        border-color: #444;
      }

      .success-message {
        background: #1a2e1a;
        color: #4caf50;
      }

      .back-link {
        color: #4caf50;
      }
    }
  </style>
</head>
<body>
  <a href="#" id="backLink" class="back-link">← Retour à la popup</a>

  <h1><img src="icons/icon48.png" alt="Bullet Icon">Paramètres Bullets</h1>

  <div id="successMessage" class="success-message">
    Paramètres sauvegardés avec succès !
  </div>

  <div class="setting-group">
    <h2>Modèle Gemini</h2>
    <div class="setting-item">
      <label for="modelSelect">Choisir le modèle :</label>
      <select id="modelSelect">
        <option value="gemini-2.5-flash-lite">Gemini 2.5 Flash Lite (Rapide et économique)</option>
        <option value="gemini-2.5-flash">Gemini 2.5 Flash (Équilibré)</option>
        <option value="gemini-2.5-pro">Gemini 2.5 Pro (Plus puissant)</option>
      </select>
    </div>
  </div>

  <div class="setting-group">
    <h2>Langue des réponses</h2>
    <div class="setting-item">
      <label for="languageSelect">Langue :</label>
      <select id="languageSelect">
        <option value="french">Français</option>
        <option value="english">English</option>
        <option value="custom">Personnalisée</option>
      </select>
      <div id="customLanguageInput" class="custom-language-input">
        <input type="text" id="customLanguage" placeholder="Ex: Español, Deutsch, 日本語...">
      </div>
    </div>
  </div>

  <div class="setting-group">
    <h2>Prompts personnalisés</h2>
    <div class="setting-item">
      <label for="summarizePrompt">Prompt de résumé :</label>
      <textarea id="summarizePrompt" placeholder="Entrez votre prompt personnalisé pour les résumés..."></textarea>
    </div>
    <div class="setting-item">
      <label for="contextPrompt">Prompt de contexte :</label>
      <textarea id="contextPrompt" placeholder="Entrez votre prompt personnalisé pour le contexte..."></textarea>
    </div>
    <div class="setting-item">
      <label for="titlePrompt">Prompt de génération de titre :</label>
      <textarea id="titlePrompt" placeholder="Entrez votre prompt personnalisé pour les titres..."></textarea>
    </div>
    <div class="setting-item">
      <label for="actionablesPrompt">Prompt d'extraction d'actions :</label>
      <textarea id="actionablesPrompt" placeholder="Entrez votre prompt personnalisé pour extraire les éléments d'action..."></textarea>
    </div>
  </div>

  <div class="setting-group">
    <h2>Actions</h2>
    <button id="saveSettings" class="button">Sauvegarder les paramètres</button>
    <button id="resetSettings" class="button secondary">Réinitialiser par défaut</button>
    <button id="exportSettings" class="button secondary">Exporter la configuration</button>
    <button id="importSettings" class="button secondary">Importer la configuration</button>
    <input type="file" id="importFile" accept=".json" style="display: none;">
  </div>

  <script src="settings.js"></script>
</body>
</html>
