# Guide d'Utilisation - Nouvel Onglet Actions

## Vue d'ensemble

L'extension Bullets dispose maintenant d'un nouvel onglet **"Actions"** qui extrait automatiquement les éléments d'action, tâches et recommandations pratiques à partir du contenu des pages web.

## Fonctionnalités

### Extraction Automatique d'Actions
- Identifie les tâches concrètes mentionnées dans le texte
- Extrait les recommandations et étapes à suivre
- Détecte les actions implicites qui découlent logiquement du contenu
- Organise les actions par catégories (Actions Immédiates, Actions à Long Terme)

### Interface Utilisateur
- Nouvel onglet "Actions" dans la popup de l'extension
- Chargement à la demande (le contenu se charge uniquement quand vous cliquez sur l'onglet)
- Formatage structuré avec titres et puces pour une lecture facile
- Intégration seamless avec les onglets existants (Summary et Context)

## Comment Utiliser

### 1. Activation de l'Extension
- Faites un clic droit sur une page web
- Sélectionnez "Bullet page" ou "Bullet selection"
- Ou utilisez le raccourci clavier Ctrl+B (Cmd+B sur Mac)

### 2. Accès à l'Onglet Actions
- Une fois la popup ouverte, cliquez sur l'onglet "Actions"
- Le système analysera automatiquement le contenu de la page
- Les éléments d'action seront extraits et affichés

### 3. Types d'Actions Extraites
- **Actions Immédiates** : Tâches à faire rapidement
- **Actions à Long Terme** : Objectifs et projets plus larges
- **Étapes Spécifiques** : Instructions détaillées avec requirements
- **Recommandations** : Suggestions d'amélioration ou bonnes pratiques

## Configuration

### Personnalisation du Prompt
1. Cliquez sur l'icône de l'extension dans la barre d'outils
2. Sélectionnez "⚙️ Paramètres"
3. Dans la section "Prompts personnalisés", modifiez le "Prompt d'extraction d'actions"
4. Sauvegardez vos modifications

### Prompt par Défaut
Le prompt par défaut est optimisé pour :
- Identifier les actions concrètes et implémentables
- Organiser par priorité et séquence logique
- Inclure les détails spécifiques (délais, ressources)
- Distinguer les actions immédiates des objectifs long terme

## Exemples d'Utilisation

### Articles de Productivité
- Extrait les techniques et méthodes à appliquer
- Identifie les étapes de mise en œuvre
- Organise les actions par urgence

### Guides Techniques
- Récupère les instructions d'installation
- Liste les prérequis et dépendances
- Identifie les étapes de configuration

### Articles d'Actualité
- Extrait les actions recommandées par les experts
- Identifie les implications pratiques
- Suggère des actions de suivi

## Intégration avec les Autres Fonctionnalités

### Sauvegarde des Actions
- Utilisez le bouton "Save" pour sauvegarder les actions extraites
- Les actions sauvegardées sont accessibles via la popup principale
- Recherche possible dans les actions sauvegardées

### Copie vers le Presse-papiers
- Le bouton "Copy" copie le contenu de l'onglet actif
- Collez facilement les actions dans votre gestionnaire de tâches
- Format compatible avec la plupart des applications de productivité

## Conseils d'Utilisation

### Pour de Meilleurs Résultats
- Utilisez sur des contenus riches en recommandations pratiques
- Fonctionne particulièrement bien avec les guides, tutoriels, et articles de conseil
- Plus efficace sur des textes structurés avec des listes et étapes

### Cas d'Usage Optimaux
- Articles de blog sur la productivité
- Guides de développement et tutoriels
- Rapports avec recommandations
- Documentation technique avec procédures

## Dépannage

### L'Onglet Actions Ne Se Charge Pas
- Vérifiez votre connexion internet
- Assurez-vous d'avoir configuré une clé API valide
- Rechargez la page et réessayez

### Aucune Action Détectée
- Le contenu peut ne pas contenir d'éléments d'action explicites
- Essayez avec un contenu plus riche en recommandations
- Vérifiez que le texte est suffisamment long et structuré

### Personnalisation du Prompt
- Modifiez le prompt dans les paramètres pour l'adapter à vos besoins
- Testez différentes formulations pour optimiser les résultats
- Sauvegardez vos configurations personnalisées

## Support

Pour toute question ou problème :
- Consultez la documentation principale dans README.md
- Vérifiez les logs de la console développeur pour les erreurs
- Assurez-vous que l'extension est à jour

---

Cette nouvelle fonctionnalité transforme l'extension Bullets en un véritable assistant de productivité, capable d'extraire automatiquement les actions concrètes de n'importe quel contenu web.
