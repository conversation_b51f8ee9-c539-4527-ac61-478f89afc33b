# Guide de Dépannage - Onglet Actions

## Erreurs Courantes et Solutions

### 1. <PERSON><PERSON><PERSON> "Cannot read properties of undefined (reading 'replace')"

**Cause :** Cette erreur se produit quand le prompt actionables n'existe pas dans les paramètres sauvegardés.

**Solution :** 
- ✅ **Corrigé automatiquement** : Le code utilise maintenant le prompt par défaut si aucun prompt personnalisé n'est configuré
- Si l'erreur persiste, réinitialisez les paramètres via l'interface des paramètres

### 2. L'onglet Actions ne se charge pas

**Symptômes :**
- L'onglet Actions reste vide
- Message "Extraction des éléments d'action..." qui ne disparaît pas
- Aucune réponse après plusieurs secondes

**Solutions :**
1. **Vérifiez votre clé API :**
   - Allez dans les paramètres de l'extension
   - Assurez-vous qu'une clé API Google Gemini valide est configurée
   - Testez avec l'onglet Summary pour vérifier que l'API fonctionne

2. **Vérifiez votre connexion internet :**
   - L'extraction d'actions nécessite une connexion internet active
   - Testez avec une autre page web

3. **Rechargez l'extension :**
   - Allez dans `chrome://extensions/`
   - Trouvez l'extension Bullets
   - Cliquez sur le bouton de rechargement (🔄)

### 3. Aucune action détectée

**Message affiché :**
```
Title: Aucune Action Identifiée
• Aucun élément d'action spécifique identifié dans ce contenu.
```

**Causes possibles :**
- Le contenu de la page ne contient pas d'éléments d'action explicites
- Le texte est trop court ou peu structuré
- Le contenu est principalement informatif sans recommandations pratiques

**Solutions :**
1. **Testez avec du contenu approprié :**
   - Articles de productivité
   - Guides pratiques et tutoriels
   - Documentation avec procédures
   - Rapports avec recommandations

2. **Utilisez la page de test :**
   - Ouvrez `test-page.html` fourni avec l'extension
   - Testez l'onglet Actions sur cette page
   - Si cela fonctionne, le problème vient du contenu original

### 4. Formatage incorrect des actions

**Symptômes :**
- Actions affichées sans structure
- Pas de titres ou de puces
- Texte brut non formaté

**Solutions :**
1. **Vérifiez le prompt personnalisé :**
   - Allez dans Paramètres → Prompts personnalisés
   - Assurez-vous que le prompt d'extraction d'actions contient les instructions de formatage
   - Réinitialisez aux valeurs par défaut si nécessaire

2. **Format attendu du prompt :**
   ```
   - Start each section with "Title: " followed by the category title
   - Use bullet points (•) for each action item under a title
   - Keep actions concise but specific (1-2 lines)
   - End each point with a period
   ```

### 5. Erreurs de console du navigateur

**Comment vérifier :**
1. Ouvrez les outils de développement (F12)
2. Allez dans l'onglet Console
3. Recherchez les erreurs en rouge

**Erreurs courantes :**

#### "Extension context invalidated"
- **Cause :** L'extension a été rechargée pendant une opération
- **Solution :** Rechargez la page web et réessayez

#### "Failed to fetch" ou "Network error"
- **Cause :** Problème de connexion à l'API Google Gemini
- **Solution :** Vérifiez votre connexion internet et votre clé API

#### "Port disconnected"
- **Cause :** Communication interrompue entre content script et background script
- **Solution :** Rechargez la page web

### 6. Performance lente

**Symptômes :**
- L'onglet Actions prend beaucoup de temps à se charger
- L'interface se fige pendant le traitement

**Solutions :**
1. **Optimisez le contenu :**
   - Évitez les pages web très longues (>10,000 mots)
   - Utilisez "Bullet selection" pour traiter seulement une partie du texte

2. **Changez de modèle :**
   - Allez dans Paramètres → Modèle Gemini
   - Utilisez "Gemini 2.5 Flash Lite" pour plus de rapidité
   - "Gemini 2.5 Pro" est plus puissant mais plus lent

### 7. Problèmes de sauvegarde des paramètres

**Symptômes :**
- Les modifications du prompt ne sont pas sauvegardées
- Retour aux valeurs par défaut après redémarrage

**Solutions :**
1. **Vérifiez les permissions :**
   - L'extension doit avoir la permission "storage"
   - Vérifiez dans `chrome://extensions/`

2. **Effacez le cache :**
   - Allez dans Paramètres de l'extension
   - Cliquez sur "Réinitialiser par défaut"
   - Reconfigurez vos paramètres personnalisés

## Tests de Diagnostic

### Test 1 : Fonctionnalité de base
1. Ouvrez `test-page.html`
2. Clic droit → "Bullet page"
3. Cliquez sur l'onglet "Actions"
4. ✅ Devrait afficher des actions extraites

### Test 2 : Configuration API
1. Testez d'abord l'onglet "Summary"
2. Si Summary fonctionne mais pas Actions, le problème est spécifique à l'onglet Actions
3. Si rien ne fonctionne, vérifiez votre clé API

### Test 3 : Prompt personnalisé
1. Allez dans Paramètres
2. Modifiez légèrement le prompt d'extraction d'actions
3. Sauvegardez et testez
4. ✅ Les modifications devraient être appliquées

## Support Avancé

### Logs détaillés
Pour obtenir plus d'informations sur les erreurs :
1. Ouvrez la console développeur (F12)
2. Activez tous les niveaux de logs
3. Reproduisez le problème
4. Copiez les messages d'erreur pour diagnostic

### Réinitialisation complète
Si tous les autres solutions échouent :
1. Désinstallez l'extension
2. Supprimez les données de navigation pour le domaine
3. Réinstallez l'extension
4. Reconfigurez vos paramètres

---

**Note :** La plupart des problèmes sont résolus en s'assurant qu'une clé API Google Gemini valide est configurée et que l'extension a les permissions nécessaires.
