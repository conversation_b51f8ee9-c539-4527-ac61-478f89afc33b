# Guide de Test - Onglet Actions

## Étapes de Test avec Logs de Débogage

### 1. Préparation
1. Ouvrez `test-page.html` dans votre navigateur
2. Ouvrez les outils de développement (F12)
3. Allez dans l'onglet Console
4. Assurez-vous que l'extension Bullets est chargée

### 2. Test de Base
1. Faites clic droit sur la page → "Bullet page"
2. La popup devrait s'ouvrir avec 3 onglets : Summary, Context, Actions
3. Cliquez sur l'onglet "Actions"

### 3. Logs à Vérifier dans la Console

#### Logs Attendus (dans l'ordre) :
```
Actions tab clicked for first time
Page text length for actions: [nombre]
fetchActionables called with text length: [nombre]
Connected to background script port
Sent callOpenAIActionables message to background
Received callOpenAIActionables request, text length: [nombre]
Starting actionables API call with config: {...}
Received message from background: {type: "data", content: "..."}
updateActionables called with content: ...
Updated actions display with: ...
```

#### Si Aucun Log n'Apparaît :
- L'extension n'est pas chargée correctement
- Rechargez l'extension dans `chrome://extensions/`

#### Si "Actions tab clicked for first time" N'Apparaît Pas :
- L'onglet Actions n'est pas créé correctement
- Vérifiez que les 3 onglets sont visibles dans la popup

#### Si "Page text length for actions: 0" :
- La fonction `getCleanedPageText()` ne récupère pas le contenu
- Testez d'abord avec l'onglet Summary pour voir s'il fonctionne

#### Si "fetchActionables called" Mais Pas de "Connected to background script port" :
- Problème de communication avec le background script
- Vérifiez que background.js est chargé

#### Si "Sent callOpenAIActionables message" Mais Pas de "Received callOpenAIActionables request" :
- Le message n'arrive pas au background script
- Vérifiez la console du background script

#### Si "Starting actionables API call" Mais Pas de "Received message from background" :
- Problème avec l'API Gemini
- Vérifiez votre clé API dans les paramètres
- Vérifiez votre connexion internet

### 4. Tests de Diagnostic

#### Test 1 : Vérifier la Clé API
1. Testez d'abord l'onglet "Summary"
2. Si Summary fonctionne mais pas Actions, le problème est spécifique à Actions
3. Si rien ne fonctionne, vérifiez votre clé API

#### Test 2 : Vérifier le Prompt
1. Allez dans Paramètres → Prompts personnalisés
2. Vérifiez que le "Prompt d'extraction d'actions" existe
3. S'il est vide, cliquez sur "Réinitialiser par défaut"

#### Test 3 : Test avec Contenu Simple
1. Sélectionnez un petit paragraphe de texte
2. Clic droit → "Bullet selection"
3. Testez l'onglet Actions sur cette sélection

### 5. Erreurs Courantes et Solutions

#### Erreur : "Actions panel not found"
**Cause :** L'onglet Actions n'est pas créé dans l'interface
**Solution :** 
- Rechargez l'extension
- Vérifiez que content.js est bien injecté

#### Erreur : "Cannot read properties of undefined"
**Cause :** Prompt actionables manquant dans la configuration
**Solution :**
- Allez dans Paramètres
- Cliquez sur "Réinitialiser par défaut"
- Sauvegardez les paramètres

#### Erreur : "Failed to fetch" ou "Network error"
**Cause :** Problème de connexion à l'API
**Solution :**
- Vérifiez votre connexion internet
- Vérifiez votre clé API Google Gemini
- Testez avec un autre modèle (Flash Lite au lieu de Pro)

#### Pas de Logs du Tout
**Cause :** Extension non chargée ou content script non injecté
**Solution :**
- Allez dans `chrome://extensions/`
- Rechargez l'extension Bullets
- Rechargez la page web
- Réessayez

### 6. Console du Background Script

Pour voir les logs du background script :
1. Allez dans `chrome://extensions/`
2. Trouvez l'extension Bullets
3. Cliquez sur "Inspecter les vues : service worker"
4. Une nouvelle fenêtre de console s'ouvre pour le background script

### 7. Test de Validation Finale

Si tout fonctionne correctement, vous devriez voir :
1. Message de chargement : "Extraction des éléments d'action..."
2. Contenu structuré avec titres et puces
3. Actions organisées par catégories (Immédiates, Long Terme, etc.)

### 8. Rapport de Bug

Si le problème persiste, collectez ces informations :
- Tous les logs de la console
- Version du navigateur
- Contenu de la page testée
- Configuration de l'extension (modèle, langue, etc.)
- Capture d'écran de l'interface

---

**Note :** Les logs de débogage ont été ajoutés temporairement pour diagnostiquer le problème. Ils peuvent être supprimés une fois le problème résolu.
