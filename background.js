/**
 * @fileoverview Background service worker for Bullets Chrome Extension
 * @description Handles API communication, error logging, caching, and extension lifecycle
 * <AUTHOR> Extension Team
 * @version 2.0
 */

//background.js

console.log('Background script loaded.');

/**
 * Enhanced error logging system for comprehensive error tracking and debugging
 * @typedef {Object} ErrorLogger
 * @property {function} log - Core logging function with structured data
 * @property {function} error - Log error-level messages
 * @property {function} warn - Log warning-level messages
 * @property {function} info - Log info-level messages
 * @property {function} storeErrorLog - Store error logs to Chrome storage
 */
const ErrorLogger = {
  /**
   * Core logging function that creates structured log entries
   * @param {string} level - Log level (error, warn, info)
   * @param {string} message - Human-readable error message
   * @param {Error|null} error - Optional error object with stack trace
   * @param {Object} context - Additional context data for debugging
   */
  log: function(level, message, error = null, context = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : null,
      context,
      extension_version: chrome.runtime.getManifest().version
    };

    // Log to console with appropriate level
    const consoleMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    if (error) {
      console[level](consoleMessage, error);
    } else {
      console[level](consoleMessage);
    }

    // Store error for debugging (keep last 50 errors)
    this.storeErrorLog(logEntry);
  },

  /**
   * Log error-level messages
   * @param {string} message - Error message
   * @param {Error|null} error - Optional error object
   * @param {Object} context - Additional debugging context
   */
  error: function(message, error = null, context = {}) {
    this.log('error', message, error, context);
  },

  /**
   * Log warning-level messages
   * @param {string} message - Warning message
   * @param {Error|null} error - Optional error object
   * @param {Object} context - Additional debugging context
   */
  warn: function(message, error = null, context = {}) {
    this.log('warn', message, error, context);
  },

  /**
   * Log info-level messages
   * @param {string} message - Info message
   * @param {Object} context - Additional debugging context
   */
  info: function(message, context = {}) {
    this.log('info', message, null, context);
  },

  /**
   * Store error logs to Chrome local storage for debugging
   * @param {Object} logEntry - Structured log entry to store
   */
  storeErrorLog: function(logEntry) {
    chrome.storage.local.get(['errorLogs'], (data) => {
      const logs = data.errorLogs || [];
      logs.push(logEntry);
      // Keep only last 50 entries to prevent unlimited growth
      if (logs.length > 50) {
        logs.splice(0, logs.length - 50);
      }
      chrome.storage.local.set({ errorLogs: logs });
    });
  }
};

// Cached configuration for API calls
let cachedConfig = null;

// Content buffering for streaming responses
let contentBuffer = '';

// Request queue to prevent concurrent API calls
let requestQueue = [];
let isProcessingRequest = false;

// Cache for API responses
const responseCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Force cache clear on startup to ensure fresh config
cachedConfig = null;

/**
 * Safely send messages to Chrome runtime ports with error handling
 * @param {Object} port - Chrome runtime port object
 * @param {*} message - Message to send to the port
 */
function safePostMessage(port, message) {
  try {
    if (port && !port.error) {
      port.postMessage(message);
    }
  } catch (error) {
    if (error.message && error.message.includes('Extension context invalidated')) {
      ErrorLogger.info('Extension context invalidated, cannot send message', { port: port?.name });
    } else {
      ErrorLogger.error('Error sending message to port', error, { port: port?.name, message });
    }
  }
}

/**
 * Convert technical error messages to user-friendly error messages
 * @param {Error|string} error - Original error object or message
 * @param {Object} context - Additional context for error categorization
 * @returns {Object} User-friendly error object with type, title, message, and suggestion
 */
function createUserFriendlyErrorMessage(error, context = {}) {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';

  // Network errors - connection issues
  if (error.name === 'TypeError' && errorMessage.includes('fetch')) {
    return {
      type: 'network',
      title: 'Connection Error',
      message: 'Unable to connect to the API. Please check your internet connection and try again.',
      suggestion: 'Check your internet connection and try again in a few moments.'
    };
  }

  // Timeout errors - request took too long
  if (error.name === 'AbortError' || errorMessage.includes('timeout')) {
    return {
      type: 'timeout',
      title: 'Request Timeout',
      message: 'The request took too long to complete. The API might be busy.',
      suggestion: 'Try again in a few moments. If the problem persists, try a shorter text selection.'
    };
  }

  // API quota/rate limit errors - usage limits exceeded
  if (errorMessage.includes('429') || errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
    return {
      type: 'quota',
      title: 'API Limit Reached',
      message: 'You\'ve reached the API usage limit. Please wait before making more requests.',
      suggestion: 'Wait a few minutes before trying again, or check your API usage limits.'
    };
  }

  // Authentication errors - invalid or missing API key
  if (errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('unauthorized')) {
    return {
      type: 'auth',
      title: 'Authentication Error',
      message: 'API key is invalid or missing. Please check your API key in settings.',
      suggestion: 'Go to the extension settings and verify your API key is correct.'
    };
  }

  // Extension context errors - Chrome extension lifecycle issues
  if (errorMessage.includes('Extension context invalidated')) {
    return {
      type: 'extension',
      title: 'Extension Error',
      message: 'The extension needs to be reloaded. Please refresh the page.',
      suggestion: 'Refresh the page and try again. If the problem continues, restart your browser.'
    };
  }

  // Default error message for unknown errors
  return {
    type: 'unknown',
    title: 'Processing Error',
    message: 'An unexpected error occurred while processing your request.',
    suggestion: 'Try again with a shorter text selection, or refresh the page if the problem persists.'
  };
}

// Create context menu items when the extension is installed or updated
chrome.runtime.onInstalled.addListener(() => {
  console.log('Extension installed.');

  // Remove any existing context menus to prevent duplicates
  chrome.contextMenus.removeAll(() => {
    // Context menu for bulleting the entire page
    chrome.contextMenus.create({
      id: "bulletPage",
      title: "Bullet page",
      contexts: ["all"]
    });

    // Context menu for bulleting selected text
    chrome.contextMenus.create({
      id: "bulletSelection",
      title: "Bullet selection",
      contexts: ["selection"]
    });
  });
});

// Handle context menu item clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  console.log('Context menu item clicked:', info.menuItemId);
  if (info.menuItemId === "bulletPage" || info.menuItemId === "bulletSelection") {
    // First, inject the content script into the tab
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['content.js']
    }, () => {
      // Now send the message to the content script
      console.log('Sending message to content script.');
      chrome.tabs.sendMessage(tab.id, {
        action: info.menuItemId === "bulletPage" ? 'summarizePage' : 'summarizeSelection'
      });
    });
  }
});

// Listen for connections from content script
chrome.runtime.onConnect.addListener(function(port) {
  console.log('Connected to port:', port.name);
  if (port.name === 'openai') {
    port.onMessage.addListener(function(msg) {
      try {
        if (msg.action === 'callOpenAI' && msg.text) {
          callOpenAIStream(msg.text, port);
        } else if (msg.action === 'generateTitle' && msg.summary) {
          generateTitle(msg.summary, port);
        } else if (msg.action === 'callOpenAIContext' && msg.text) {
          callOpenAIContextStream(msg.text, port);
        } else if (msg.action === 'callOpenAIActionables' && msg.text) {
          console.log('Received callOpenAIActionables request, text length:', msg.text.length);
          callOpenAIActionablesStream(msg.text, port);
        }
      } catch (error) {
        if (error.message.includes('Extension context invalidated')) {
          ErrorLogger.info('Extension context invalidated, ignoring message', { port: port.name });
          return;
        }
        ErrorLogger.error('Error handling message from port', error, { port: port.name, message: msg });
      }
    });

    port.onDisconnect.addListener(function() {
      if (chrome.runtime.lastError) {
        ErrorLogger.info('Port disconnected', null, {
          port: port.name,
          lastError: chrome.runtime.lastError.message
        });
      }
    });
  }
});

// Listen for error logs from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'logError' && message.logEntry) {
    ErrorLogger.storeErrorLog(message.logEntry);
    sendResponse({ success: true });
  }

  // Add test error endpoint for testing error handling
  if (message.action === 'testError') {
    ErrorLogger.info('Test error requested', { testType: message.testType });
    if (message.testType === 'network') {
      // Simulate network error
      setTimeout(() => {
        const networkError = new TypeError('Failed to fetch');
        networkError.name = 'TypeError';
        const friendlyError = createUserFriendlyErrorMessage(networkError);
        sendResponse({ success: true, error: friendlyError });
      }, 100);
    } else if (message.testType === 'auth') {
      // Simulate auth error
      setTimeout(() => {
        const authError = new Error('401 Unauthorized');
        const friendlyError = createUserFriendlyErrorMessage(authError);
        sendResponse({ success: true, error: friendlyError });
      }, 100);
    } else {
      sendResponse({ success: false, error: 'Unknown test type' });
    }
    return true; // Keep message channel open for async response
  }
});

/**
 * Retrieve and cache extension configuration settings from Chrome storage
 * @returns {Promise<Object>} Configuration object with API settings and user preferences
 */
async function getConfig() {
  if (cachedConfig) {
    return cachedConfig;
  }

  // Cache configuration promise to prevent multiple simultaneous storage calls
  cachedConfig = new Promise((resolve) => {
    chrome.storage.local.get(['customApiKey', 'bulletsSettings'], (data) => {
      const settings = data.bulletsSettings || {};
      resolve({
        API_ENDPOINT: 'https://generativelanguage.googleapis.com/v1beta/chat/completions',
        MODEL: settings.model || 'gemini-2.5-flash-lite',
        TIMEOUT_MS: 30000,
        TEMPERATURE: 0.7,
        API_KEY: data.customApiKey || 'api-key',
        LANGUAGE: settings.language || 'french',
        CUSTOM_LANGUAGE: settings.customLanguage || '',
        PROMPTS: settings.prompts || getDefaultPrompts()
      });
    });
  });
  return cachedConfig;
}

/**
 * Process queued requests to prevent concurrent API calls
 */
async function processRequestQueue() {
  if (isProcessingRequest || requestQueue.length === 0) {
    return;
  }

  isProcessingRequest = true;
  const { request, resolve, reject } = requestQueue.shift();

  try {
    const result = await request();
    resolve(result);
  } catch (error) {
    reject(error);
  } finally {
    isProcessingRequest = false;
    // Process next request in queue
    setTimeout(processRequestQueue, 100); // Small delay to prevent overwhelming API
  }
}

/**
 * Queue an API request to prevent concurrent calls
 */
function queueApiRequest(request) {
  return new Promise((resolve, reject) => {
    requestQueue.push({ request, resolve, reject });
    processRequestQueue();
  });
}

/**
 * Get cached response if available and not expired
 */
function getCachedResponse(key) {
  const cached = responseCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  responseCache.delete(key);
  return null;
}

/**
 * Cache API response
 */
function setCachedResponse(key, data) {
  responseCache.set(key, {
    data,
    timestamp: Date.now()
  });
}

/**
 * Consolidated error handler for API fetch responses.
 */
async function handleErrorResponse(response, port, context = {}) {
  try {
    const text = await response.text();
    ErrorLogger.error('API error response received', null, {
      status: response.status,
      statusText: response.statusText,
      responseBody: text.substring(0, 500),
      ...context
    });

    let errorMsg = text;
    let parsedError = null;

    try {
      const errorData = JSON.parse(text);
      parsedError = errorData;
      errorMsg = errorData.error?.message || text;
    } catch (parseError) {
      ErrorLogger.warn('Could not parse error response as JSON', parseError, { responseText: text.substring(0, 200) });
    }

    // Create user-friendly error message
    const friendlyError = createUserFriendlyErrorMessage({ message: errorMsg }, { status: response.status });

    safePostMessage(port, {
      type: 'error',
      error: friendlyError.message,
      title: friendlyError.title,
      suggestion: friendlyError.suggestion,
      errorType: friendlyError.type,
      retryable: ['network', 'timeout', 'unknown'].includes(friendlyError.type)
    });
  } catch (e) {
    ErrorLogger.error('Error handling error response', e, context);
    const friendlyError = createUserFriendlyErrorMessage(e);
    safePostMessage(port, {
      type: 'error',
      error: friendlyError.message,
      title: friendlyError.title,
      suggestion: friendlyError.suggestion,
      errorType: friendlyError.type,
      retryable: false
    });
  }
}

/**
 * Call Gemini AI API with streaming response for text summarization
 * Enhanced with request queuing, caching, and comprehensive error handling
 * @param {string} text - Text content to summarize
 * @param {Object} port - Chrome runtime port for communication with content script
 * @param {number} retryCount - Current retry attempt count
 * @returns {Promise<void>}
 */
async function callOpenAIStream(text, port, retryCount = 0) {
  const CONFIG = await getConfig();
  const MAX_RETRIES = 3;

  // Log API call initiation with context
  ErrorLogger.info('Starting API call', null, {
    endpoint: CONFIG.API_ENDPOINT,
    model: CONFIG.MODEL,
    textLength: text.length,
    retryCount
  });

  // Validate required parameters
  if (!text || !port) {
    ErrorLogger.error('Missing required parameters for API call', null, { hasText: !!text, hasPort: !!port });
    const error = createUserFriendlyErrorMessage({ message: 'Invalid parameters for API call' });
    safePostMessage(port, {
      type: 'error',
      error: error.message,
      title: error.title,
      suggestion: error.suggestion,
      errorType: error.type,
      retryable: false
    });
    return;
  }

  // Check cache for similar requests
  const cacheKey = `${CONFIG.MODEL}_${text.substring(0, 100)}_${CONFIG.LANGUAGE}`;
  const cachedResponse = getCachedResponse(cacheKey);
  if (cachedResponse) {
    console.log('Using cached response');
    safePostMessage(port, { type: 'data', content: cachedResponse });
    safePostMessage(port, { type: 'done' });
    return;
  }

  // Use the prompt template for summarization
  const prompt = await PROMPT_TEMPLATES.summarize(text);

  const body = JSON.stringify({
    model: CONFIG.MODEL,
    messages: [{ role: 'user', content: prompt }],
    temperature: CONFIG.TEMPERATURE,
    stream: true
  });
  console.log('Request body:', body);

  // Queue the API request to prevent concurrent calls
  await queueApiRequest(async () => {
    // Set up a timeout for the fetch
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

    try {
      const response = await fetch(CONFIG.API_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CONFIG.API_KEY}`
        },
        body,
        signal: controller.signal
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        await handleErrorResponse(response, port);
        return;
      }

      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          // Cache the full response for future requests
          setCachedResponse(cacheKey, fullContent);
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            setCachedResponse(cacheKey, fullContent);
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              fullContent += content;
              // Buffer and clean content before sending
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON:', error);
          }
        }
      }
    } catch (error) {
      clearTimeout(timeout);

      if (error.name === 'AbortError') {
        ErrorLogger.warn('API request timed out', error, { timeout: CONFIG.TIMEOUT_MS });
        const friendlyError = createUserFriendlyErrorMessage(error);

        // Retry logic for timeout errors
        if (retryCount < MAX_RETRIES) {
          ErrorLogger.info(`Retrying API call after timeout (${retryCount + 1}/${MAX_RETRIES})`);
          safePostMessage(port, {
            type: 'retry',
            message: `${friendlyError.message} Retrying... (${retryCount + 1}/${MAX_RETRIES})`
          });
          setTimeout(() => callOpenAIStream(text, port, retryCount + 1), 1000 * (retryCount + 1));
          return;
        }

        safePostMessage(port, {
          type: 'error',
          error: friendlyError.message,
          title: friendlyError.title,
          suggestion: friendlyError.suggestion,
          errorType: friendlyError.type,
          retryable: false
        });
      } else if (error.message && error.message.includes('Extension context invalidated')) {
        ErrorLogger.info('Extension context invalidated during API call, ignoring');
        return;
      } else {
        ErrorLogger.error('Error calling API', error, { textLength: text.length, model: CONFIG.MODEL });

        const friendlyError = createUserFriendlyErrorMessage(error);

        // Retry logic for network errors
        if (friendlyError.type === 'network' && retryCount < MAX_RETRIES) {
          ErrorLogger.info(`Retrying API call after network error (${retryCount + 1}/${MAX_RETRIES})`);
          safePostMessage(port, {
            type: 'retry',
            message: `${friendlyError.message} Retrying... (${retryCount + 1}/${MAX_RETRIES})`
          });
          setTimeout(() => callOpenAIStream(text, port, retryCount + 1), 2000 * (retryCount + 1));
          return;
        }

        safePostMessage(port, {
          type: 'error',
          error: friendlyError.message,
          title: friendlyError.title,
          suggestion: friendlyError.suggestion,
          errorType: friendlyError.type,
          retryable: friendlyError.type === 'network' || friendlyError.type === 'timeout'
        });
      }
    }
  });
}

/**
 * Generate title from summary using async/await.
 */
async function generateTitle(summary, port) {
  const CONFIG = await getConfig();
  console.log('Starting title generation...');

  if (!summary || !port) {
    console.error('Missing required parameters for title generation');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for title generation' });
    return;
  }

  const prompt = await PROMPT_TEMPLATES.generateTitle(summary);

  // Set up a timeout for the fetch
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

  try {
    const response = await fetch(CONFIG.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.API_KEY}`
      },
      body: JSON.stringify({
        model: CONFIG.MODEL,
        messages: [{ role: 'user', content: prompt }],
        temperature: CONFIG.TEMPERATURE,
        stream: false
      }),
      signal: controller.signal
    });

    console.log('Title generation response status:', response.status);

    if (!response.ok) {
      await handleErrorResponse(response, port);
      return;
    }

    const data = await response.json();
    console.log('Title generation API response:', data);

    // Extract title using the expected response format
    const title = data.choices?.[0]?.message?.content;
    if (!title) {
      throw new Error('No title found in response');
    }

    // Clean and process the title
    const cleanTitle = title
      .replace(/['"]/g, '')
      .replace(/\n/g, ' ')
      .replace(/^Title: /i, '')
      .trim();

    console.log('Final processed title:', cleanTitle);
    safePostMessage(port, { type: 'title', title: cleanTitle });
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Title generation request timed out');
      safePostMessage(port, { type: 'error', error: 'Title generation request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during title generation, ignoring');
      return;
    } else {
      console.error('Error in title generation:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Call the API for providing context (with streaming) using async/await.
 */
async function callOpenAIContextStream(text, port) {
  const CONFIG = await getConfig();

  console.log('Starting context API call with config:', {
    endpoint: CONFIG.API_ENDPOINT,
    model: CONFIG.MODEL,
    textLength: text.length
  });

  if (!text || !port) {
    console.error('Missing required parameters for context API call');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for context API call' });
    return;
  }

  // Use the prompt template for providing context information
  const prompt = await PROMPT_TEMPLATES.provideContext(text);

  // Set up an abort controller with a timeout
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

  const body = JSON.stringify({
    model: CONFIG.MODEL,
    messages: [{ role: 'user', content: prompt }],
    temperature: CONFIG.TEMPERATURE,
    stream: true
  });
  console.log('Context request body:', body);

  // Check cache for similar requests
  const cacheKey = `${CONFIG.MODEL}_${text.substring(0, 100)}_${CONFIG.LANGUAGE}`;
  const cachedResponse = getCachedResponse(cacheKey);
  if (cachedResponse) {
    console.log('Using cached context response');
    safePostMessage(port, { type: 'data', content: cachedResponse });
    safePostMessage(port, { type: 'done' });
    return;
  }

  try {
    const response = await fetch(CONFIG.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.API_KEY}`
      },
      body,
      signal: controller.signal
    });
    console.log('Context response status:', response.status);

    if (!response.ok) {
      await handleErrorResponse(response, port);
      return;
    } else {
      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          // Cache the full response for future requests
          setCachedResponse(cacheKey, fullContent);
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            setCachedResponse(cacheKey, fullContent);
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              fullContent += content;
              // Buffer and clean content before sending
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON:', error);
          }
        }
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Request timed out');
      safePostMessage(port, { type: 'error', error: 'Request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during API call, ignoring');
      return;
    } else {
      console.error('Error calling Gemini API:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Call the API for extracting actionables (with streaming) using async/await.
 */
async function callOpenAIActionablesStream(text, port) {
  const CONFIG = await getConfig();

  console.log('Starting actionables API call with config:', {
    endpoint: CONFIG.API_ENDPOINT,
    model: CONFIG.MODEL,
    textLength: text.length
  });

  if (!text || !port) {
    console.error('Missing required parameters for actionables API call');
    port?.postMessage({ type: 'error', error: 'Invalid parameters for actionables API call' });
    return;
  }

  // Use the prompt template for extracting actionables
  const prompt = await PROMPT_TEMPLATES.extractActionables(text);

  // Set up an abort controller with a timeout
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), CONFIG.TIMEOUT_MS);

  const body = JSON.stringify({
    model: CONFIG.MODEL,
    messages: [{ role: 'user', content: prompt }],
    temperature: CONFIG.TEMPERATURE,
    stream: true
  });
  console.log('Actionables request body:', body);

  // Check cache for similar requests
  const cacheKey = `actionables_${CONFIG.MODEL}_${text.substring(0, 100)}_${CONFIG.LANGUAGE}`;
  const cachedResponse = getCachedResponse(cacheKey);
  if (cachedResponse) {
    console.log('Using cached actionables response');
    safePostMessage(port, { type: 'data', content: cachedResponse });
    safePostMessage(port, { type: 'done' });
    return;
  }

  try {
    const response = await fetch(CONFIG.API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.API_KEY}`
      },
      body,
      signal: controller.signal
    });
    console.log('Actionables response status:', response.status);

    if (!response.ok) {
      await handleErrorResponse(response, port);
      return;
    } else {
      // Process the streamed response using a while loop
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let fullContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          safePostMessage(port, { type: 'done' });
          // Cache the full response for future requests
          setCachedResponse(cacheKey, fullContent);
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');
        for (const line of lines) {
          const message = line.replace(/^data: /, '');
          if (message === '[DONE]') {
            safePostMessage(port, { type: 'done' });
            setCachedResponse(cacheKey, fullContent);
            return;
          }
          try {
            const parsed = JSON.parse(message);
            const content = parsed.choices[0].delta.content;
            if (content) {
              fullContent += content;
              // Buffer and clean content before sending
              const cleanedContent = bufferAndCleanContent(content);
              if (cleanedContent) {
                safePostMessage(port, { type: 'data', content: cleanedContent });
              }
            }
          } catch (error) {
            console.error('Error parsing JSON:', error);
          }
        }
      }
    }
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error('Request timed out');
      safePostMessage(port, { type: 'error', error: 'Request timed out after 30 seconds' });
    } else if (error.message && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated during API call, ignoring');
      return;
    } else {
      console.error('Error calling Gemini API:', error);
      safePostMessage(port, { type: 'error', error: error.toString() });
    }
  } finally {
    clearTimeout(timeout);
  }
}

/**
 * Get default prompt templates
 */
function getDefaultPrompts() {
  return {
    summarize: `You are an expert reporter tasked with creating clear, engaging, and informative outlines of various texts. Your goal is to capture the most important details while presenting them in a structured and easily digestible format.

Please follow these steps to create your summary:

1. Carefully read through the entire text and:
   a. Identify the key takeaways in the text.
   b. For each takeaway, identify the most important and interesting points.
   c. Present each point clearly and concisely, using analogies or relatable examples.
   d. Ensure you're capturing the most salient details from the original text.
   e. Check that each point can be expressed concisely in 1-2 lines (15-30 words).

Based on your analysis, create a structured outline summary following these rules:
   - The first section title is "Title: Key Takeaways".
   - The following sections go into more detail on each takeaway.
   - Start each section with "Title: " followed by title of the takeaway.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (1-2 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Your final output should follow this exact format:

Title: [Key takeaways]
• takeaway 1
• takeaway 2
...

Title: next takeaways
• [Point 1]
• [Point 2]

...

Remember to make your summary engaging and informative, capturing the most important details from the original text while tailoring it to the target audience. It is essential for you not to repeat yourself.
Here is the text you need to summarize:
{TEXT}`,

    context: `Provide background information about the topic, that's not in the article. If the event has a long timeline, explain the last significant events leading up to it. Be concise and to the point. Use bullet points, but no ** or sub-bullets.
Your final output should follow this exact format:

   - Start each section with "Title: " followed by title of the section.
   - Use bullet points (•) for each key point under a title.
   - Keep points concise (3-5 lines) and engaging.
   - End each point with a period.
   - Do not use sub-bullets or nested points.
   - Do not use any extra formatting or special characters.

Here is an example of the format you should follow:

Title: [Background information]
• background 1
• background 2
...
Title: [Approximate Timeline]
• Event 1
• Event 2
...

Here is the text you need to provide background information for:
{TEXT}`,

    title: `Create a short title (3-7 words) that captures the main topic.
Only output the title itself, with no prefix or formatting.

Text to summarize:
{TEXT}`,

    actionables: `You are a productivity expert tasked with extracting actionable items from the given text. Your goal is to identify concrete actions, tasks, recommendations, and next steps that readers can implement.

Instructions:
1. Identify all actionable items, tasks, recommendations, and next steps mentioned in the text
2. Extract implied actions that would logically follow from the information presented
3. Organize actions by priority or logical sequence when possible
4. Include specific details like deadlines, requirements, or resources needed
5. Focus on practical, implementable actions rather than abstract concepts

Format your response following these rules:
   - Start each section with "Title: " followed by the category title
   - Use bullet points (•) for each action item under a title
   - Keep actions concise but specific (1-2 lines)
   - End each point with a period
   - Do not use sub-bullets or nested points
   - Do not use any extra formatting or special characters

Your final output should follow this exact format:

Title: Actions Immédiates
• action 1
• action 2
...

Title: Actions à Long Terme
• action 1
• action 2
...

If no clear actions are present, respond with:
Title: Aucune Action Identifiée
• Aucun élément d'action spécifique identifié dans ce contenu.

Here is the text to analyze:
{TEXT}`
  };
}

/**
 * Get language instruction based on settings
 */
function getLanguageInstruction(config) {
  switch (config.LANGUAGE) {
    case 'french':
      return 'IMPORTANT: You must reply ONLY in French. All your response must be in French language.';
    case 'english':
      return 'IMPORTANT: You must reply ONLY in English. All your response must be in English language.';
    case 'custom':
      return config.CUSTOM_LANGUAGE ? `IMPORTANT: You must reply ONLY in ${config.CUSTOM_LANGUAGE}. All your response must be in ${config.CUSTOM_LANGUAGE} language.` : '';
    default:
      return 'IMPORTANT: You must reply ONLY in French. All your response must be in French language.';
  }
}



// Prompt templates used for summarization, title generation, and providing context
const PROMPT_TEMPLATES = {
  summarize: async (text) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const summarizePrompt = CONFIG.PROMPTS.summarize || getDefaultPrompts().summarize;
    const template = summarizePrompt.replace('{TEXT}', text);
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  },

  generateTitle: async (summary) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const titlePrompt = CONFIG.PROMPTS.title || getDefaultPrompts().title;
    const template = titlePrompt.replace('{TEXT}', summary.slice(0, 500));
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  },

  provideContext: async (text) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const contextPrompt = CONFIG.PROMPTS.context || getDefaultPrompts().context;
    const template = contextPrompt.replace('{TEXT}', text);
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  },

  extractActionables: async (text) => {
    const CONFIG = await getConfig();
    const languageInstruction = getLanguageInstruction(CONFIG);
    const actionablesPrompt = CONFIG.PROMPTS.actionables || getDefaultPrompts().actionables;
    const template = actionablesPrompt.replace('{TEXT}', text);
    // Add language instruction at the beginning of the prompt for better effectiveness
    return languageInstruction ? `${languageInstruction}\n\n${template}` : template;
  }
};

const FORMAT_MARKERS = /^(Title:|•|-|\*)/;

/**
 * Buffer and clean content received via streaming.
 * Enhanced with intelligent chunking and performance optimization
 */
function bufferAndCleanContent(content) {
  if (!contentBuffer) {
    contentBuffer = '';
  }

  contentBuffer += content;

  // Only send complete words/phrases to avoid UI flickering
  const lastWordBoundary = findLastWordBoundary(contentBuffer);

  // Enhanced logic: send content when buffer reaches minimum size or complete boundary
  const MIN_CHUNK_SIZE = 5; // Minimum characters to send
  const MAX_CHUNK_SIZE = 100; // Maximum characters to buffer before forcing send

  if (lastWordBoundary > 0 && (lastWordBoundary >= MIN_CHUNK_SIZE || contentBuffer.length >= MAX_CHUNK_SIZE)) {
    const contentToSend = contentBuffer.substring(0, lastWordBoundary);
    contentBuffer = contentBuffer.substring(lastWordBoundary);

    // Clean the content to remove artifacts
    const cleaned = contentToSend
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, '\n')
      .trim();

    return cleaned || null;
  }

  return null;
}

/**
 * Find the last word boundary in the buffer
 */
function findLastWordBoundary(text) {
  const wordBoundaries = [
    /\s+/g,          // whitespace
    /[.,!?;:]/g,     // punctuation
    /\n/g           // newlines
  ];

  let lastBoundary = -1;
  for (const regex of wordBoundaries) {
    let match;
    while ((match = regex.exec(text)) !== null) {
      lastBoundary = match.index + match[0].length;
    }
  }

  return lastBoundary;
}



// Optimize the Save Note logic using async/await for asynchronous operations
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'clearConfigCache') {
    // Clear the cached configuration
    cachedConfig = null;
    sendResponse({ success: true });
    return true;
  }

  if (request.action === 'saveNote') {
    console.log('Received save note request:', request.note);

    (async () => {
      try {
        if (!request.note || !request.note.title || !request.note.content) {
          throw new Error('Invalid note data');
        }
        const savedNotes = await new Promise((resolve, reject) => {
          chrome.storage.local.get('savedNotes', (data) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(data.savedNotes || []);
            }
          });
        });

        savedNotes.push(request.note);

        await new Promise((resolve, reject) => {
          chrome.storage.local.set({ savedNotes }, () => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve();
            }
          });
        });

        console.log('Note saved successfully');
        sendResponse({ success: true });
      } catch (error) {
        console.error('Error saving note:', error);
        sendResponse({ success: false, error: error.message });
      }
    })();

    return true; // Keep the message channel open for async response
  }
});
